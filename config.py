"""Configuration settings for the Generator-Validator Agent System."""

import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Config:
    """Configuration class for the G-V Agent System."""
    
    # OpenRouter API Configuration
    OPENROUTER_API_KEY = os.getenv("OPENROUTER_API_KEY")
    OPENROUTER_BASE_URL = "https://openrouter.ai/api/v1"
    
    # Model Configuration
    DEFAULT_MODEL = "anthropic/claude-3.5-sonnet"
    GENERATOR_MODEL = "anthropic/claude-3.5-sonnet"
    VALIDATOR_MODEL = "anthropic/claude-3.5-sonnet"
    
    # Agent Configuration
    MAX_RETRIES = 3
    TIMEOUT = 60
    
    # Test Case Configuration
    DEFAULT_NUM_COMMANDS = 20
    MAX_TEST_CASES_PER_COMMAND = 5
    
    # Compilation and Execution
    COMPILE_TIMEOUT = 30
    EXECUTION_TIMEOUT = 10
    
    @classmethod
    def validate(cls):
        """Validate configuration settings."""
        if not cls.OPENROUTER_API_KEY:
            print("Warning: OPENROUTER_API_KEY is not set. Please set it in your .env file.")
            print("The system will not work without a valid API key.")
            return False

        return True

# Validate configuration on import (but don't fail)
Config.validate()
