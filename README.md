# Generator-Validator Agent System

Implementation of the Generator-Validator agents from the paper "CodeContests+: High-Quality Test Case Generation for Competitive Programming" (arXiv:2506.05817).

## Overview

This system implements an LLM-based agent system that creates high-quality test cases for competitive programming problems. It consists of two main agents:

1. **Generator Agent**: Creates test case generators that produce diverse, comprehensive test cases
2. **Validator Agent**: Creates validators that ensure generated test cases satisfy all problem constraints

## Architecture

The system follows the architecture described in the paper:

```
Problem Statement → Generator Agent → Generator Program → Test Cases
                                           ↓
                    Validator Agent → Validator Program → Validation
                                           ↓
                    Feedback Loop ← Invalid Cases ← Validation Results
```

## Features

- **LLM-Powered Generation**: Uses OpenRouter API with Claude 3.5 Sonnet for intelligent code generation
- **Iterative Refinement**: Agents learn from compilation errors and validation failures
- **Comprehensive Validation**: Ensures all problem constraints are satisfied
- **Testlib Integration**: Uses testlib library for robust test case generation and validation
- **Automatic Compilation**: Compiles and tests generated C++ code
- **Feedback System**: Provides detailed feedback for agent improvement

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd CodeContests+
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Set up your OpenRouter API key in `.env`:
```bash
OPENROUTER_API_KEY=your_api_key_here
```

4. Ensure you have g++ compiler installed:
```bash
g++ --version
```

## Usage

### Basic Usage

```python
from data_structures import Problem
from gv_system import GeneratorValidatorSystem

# Create a problem
problem = Problem(
    id="example_001",
    title="Sum of Two Numbers",
    statement="Given two integers A and B, output their sum.",
    constraints="1 ≤ A, B ≤ 10^9",
    input_format="Two integers A and B separated by space",
    output_format="Single integer A + B",
    sample_inputs=["5 3"],
    sample_outputs=["8"]
)

# Generate test cases
gv_system = GeneratorValidatorSystem()
test_cases, gen_result, val_result = gv_system.generate_test_cases(problem)

print(f"Generated {len(test_cases)} test cases")
```

### Running the Demo

```bash
python example_usage.py
```

## System Components

### Generator Agent (`generator_agent.py`)
- Analyzes problem constraints and edge cases
- Generates C++ code using testlib library
- Creates diverse generator commands
- Handles feedback for iterative improvement

### Validator Agent (`validator_agent.py`)
- Identifies all problem constraints
- Creates comprehensive validation logic
- Provides detailed error messages
- Ensures constraint compliance

### Compiler (`compiler.py`)
- Compiles C++ code with appropriate flags
- Executes programs with timeout protection
- Handles compilation and runtime errors
- Manages temporary files

### Main System (`gv_system.py`)
- Orchestrates the Generator-Validator workflow
- Manages iterative refinement process
- Validates test cases against constraints
- Provides comprehensive error handling

## Configuration

Edit `config.py` to customize:

- **Models**: Choose different LLM models for each agent
- **Timeouts**: Adjust compilation and execution timeouts
- **Iterations**: Set maximum refinement iterations
- **Test Cases**: Configure number of test cases to generate

## Key Features from the Paper

### 1. Generator Program Design
- Uses command-line arguments for parameterization
- Generates diverse test cases (small, medium, large)
- Includes edge cases and adversarial inputs
- Ensures deterministic generation with seeds

### 2. Validator Program Design
- Checks all problem constraints thoroughly
- Provides specific error messages with locations
- Handles edge cases and boundary conditions
- Uses testlib validation functions

### 3. Supervision and Feedback
- Automatic error detection and reporting
- Iterative refinement based on failures
- Sample input validation for validator testing
- Comprehensive constraint checking

### 4. Quality Assurance
- Compilation verification for both programs
- Test case validation against constraints
- Statistical analysis of valid vs invalid cases
- Automatic retry with improved implementations

## Example Problems Supported

- **Arithmetic Problems**: Sum, product, mathematical operations
- **Graph Problems**: Connected components, shortest paths, trees
- **Array Problems**: Sorting, searching, dynamic programming
- **String Problems**: Pattern matching, transformations
- **Geometric Problems**: Points, lines, polygons

## Limitations

- Requires C++ compiler (g++)
- Limited to problems expressible in competitive programming format
- Depends on LLM quality and API availability
- May require multiple iterations for complex constraints

## Contributing

1. Fork the repository
2. Create a feature branch
3. Implement your changes
4. Add tests and documentation
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Citation

If you use this system in your research, please cite the original paper:

```bibtex
@article{wang2025codecontests,
  title={CodeContests+: High-Quality Test Case Generation for Competitive Programming},
  author={Wang, Zihan and Liu, Siyao and Sun, Yang and Li, Hongyan and Shen, Kai},
  journal={arXiv preprint arXiv:2506.05817},
  year={2025}
}
```

## Acknowledgments

- Original paper authors for the Generator-Validator architecture
- MikeMirzayanov for the testlib library
- OpenRouter for LLM API access
- Competitive programming community for problem formats and standards
