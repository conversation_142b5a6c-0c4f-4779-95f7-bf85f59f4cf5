"""Main Generator-Validator Agent System."""

import logging
from typing import List, <PERSON><PERSON>, <PERSON><PERSON>
from data_structures import (
    Problem, TestCase, Generator<PERSON><PERSON>ult, <PERSON>ida<PERSON><PERSON><PERSON><PERSON>, 
    Agent<PERSON><PERSON>back, ValidationResult, ValidationError
)
from generator_agent import GeneratorAgent
from validator_agent import ValidatorAgent
from compiler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CompilerError, ExecutionError
from config import Config

logger = logging.getLogger(__name__)

class GeneratorValidatorSystem:
    """Main system orchestrating the Generator-Validator agents."""
    
    def __init__(self):
        """Initialize the G-V system."""
        self.generator_agent = GeneratorAgent()
        self.validator_agent = ValidatorAgent()
        self.compiler = CppCompiler()
    
    def generate_test_cases(
        self, 
        problem: Problem, 
        max_iterations: int = 3
    ) -> Tuple[List[TestCase], GeneratorResult, ValidatorResult]:
        """
        Generate high-quality test cases for a problem using the G-V system.
        
        Args:
            problem: The problem to generate test cases for
            max_iterations: Maximum number of refinement iterations
            
        Returns:
            Tuple of (test_cases, final_generator_result, final_validator_result)
        """
        logger.info(f"Starting test case generation for problem: {problem.title}")
        
        generator_result = None
        validator_result = None
        generator_feedback = None
        validator_feedback = None
        test_cases = []
        
        for iteration in range(max_iterations):
            logger.info(f"Iteration {iteration + 1}/{max_iterations}")
            
            # Step 1: Generate the generator
            logger.info("Generating test case generator...")
            generator_result = self.generator_agent.generate_test_generator(
                problem, generator_feedback
            )
            
            if not generator_result.generator_code:
                logger.error("Failed to generate generator code")
                generator_feedback = AgentFeedback(
                    success=False,
                    message="No generator code was produced",
                    errors=["Empty generator code"],
                    suggestions=["Please provide complete C++ generator code"]
                )
                continue
            
            # Step 2: Compile the generator
            try:
                generator_executable = self.compiler.compile_code(
                    generator_result.generator_code, "generator"
                )
                generator_result.compilation_successful = True
                logger.info("Generator compiled successfully")
            except CompilerError as e:
                logger.error(f"Generator compilation failed: {str(e)}")
                generator_result.compilation_error = str(e)
                generator_feedback = AgentFeedback(
                    success=False,
                    message="Generator compilation failed",
                    errors=[str(e)],
                    suggestions=["Fix compilation errors", "Check C++ syntax"]
                )
                continue
            
            # Step 3: Generate the validator
            logger.info("Generating validator...")
            validator_result = self.validator_agent.generate_validator(
                problem, validator_feedback
            )
            
            if not validator_result.validator_code:
                logger.error("Failed to generate validator code")
                validator_feedback = AgentFeedback(
                    success=False,
                    message="No validator code was produced",
                    errors=["Empty validator code"],
                    suggestions=["Please provide complete C++ validator code"]
                )
                continue
            
            # Step 4: Compile the validator
            try:
                validator_executable = self.compiler.compile_code(
                    validator_result.validator_code, "validator"
                )
                validator_result.compilation_successful = True
                logger.info("Validator compiled successfully")
            except CompilerError as e:
                logger.error(f"Validator compilation failed: {str(e)}")
                validator_result.compilation_error = str(e)
                validator_feedback = AgentFeedback(
                    success=False,
                    message="Validator compilation failed",
                    errors=[str(e)],
                    suggestions=["Fix compilation errors", "Check C++ syntax"]
                )
                continue
            
            # Step 5: Test validator with sample inputs
            validator_works = True
            validator_errors = []
            
            for i, sample_input in enumerate(problem.sample_inputs):
                try:
                    # Add newline to sample input if not present
                    test_input = sample_input if sample_input.endswith('\n') else sample_input + '\n'
                    stdout, stderr, returncode = self.compiler.execute_program(
                        validator_executable, test_input
                    )
                    
                    if returncode != 0:
                        error_msg = f"Validator rejected sample input {i+1}: {stderr}"
                        logger.warning(error_msg)
                        validator_errors.append(error_msg)
                        validator_works = False
                        
                except ExecutionError as e:
                    error_msg = f"Validator execution failed on sample {i+1}: {str(e)}"
                    logger.error(error_msg)
                    validator_errors.append(error_msg)
                    validator_works = False
            
            if not validator_works:
                validator_feedback = AgentFeedback(
                    success=False,
                    message="Validator failed on sample inputs",
                    errors=validator_errors,
                    suggestions=["Fix validator logic", "Check constraint validation"]
                )
                continue
            
            # Step 6: Generate test cases and validate them
            test_cases = []
            invalid_cases = []
            
            for command in generator_result.commands[:10]:  # Limit to first 10 commands
                try:
                    # Parse command arguments - remove "./gen" prefix
                    cmd_parts = command.command.split()
                    if cmd_parts[0] in ["./gen", "gen"]:
                        args = cmd_parts[1:]  # Skip the "./gen" part
                    else:
                        args = cmd_parts
                    
                    # Generate test input
                    stdout, stderr, returncode = self.compiler.execute_program(
                        generator_executable, "", args
                    )
                    
                    if returncode != 0:
                        logger.warning(f"Generator command failed: {command.command}")
                        continue
                    
                    test_input = stdout.strip()

                    # Validate the generated input (add newline if not present)
                    test_input_with_newline = test_input if test_input.endswith('\n') else test_input + '\n'
                    val_stdout, val_stderr, val_returncode = self.compiler.execute_program(
                        validator_executable, test_input_with_newline
                    )
                    
                    if val_returncode != 0:
                        invalid_cases.append({
                            'command': command.command,
                            'input': test_input,
                            'error': val_stderr
                        })
                        continue
                    
                    # Generate expected output (would need reference solution)
                    # For now, we'll create test case without expected output
                    test_case = TestCase(
                        input_data=test_input,
                        expected_output="",  # Would need reference solution
                        description=command.description
                    )
                    test_cases.append(test_case)
                    
                except Exception as e:
                    logger.error(f"Error processing command {command.command}: {str(e)}")
                    continue
            
            # Step 7: Check if we have enough valid test cases
            if len(invalid_cases) > len(test_cases) * 0.3:  # More than 30% invalid
                generator_feedback = AgentFeedback(
                    success=False,
                    message=f"Too many invalid test cases generated: {len(invalid_cases)}",
                    errors=[case['error'] for case in invalid_cases[:5]],
                    suggestions=[
                        "Review constraint handling in generator",
                        "Ensure all generated inputs satisfy problem constraints"
                    ]
                )
                continue
            
            # Success! We have valid test cases
            logger.info(f"Successfully generated {len(test_cases)} valid test cases")
            generator_result.test_cases = test_cases
            break
        
        return test_cases, generator_result, validator_result
    
    def cleanup(self):
        """Clean up resources."""
        self.compiler.cleanup()
