#include "testlib.h"
#include <iostream>

using namespace std;

int main(int argc, char* argv[]) {
    registerGen(argc, argv, 1);
    
    // Parse command line arguments
    // If no range is specified, use full range
    long long minVal = 1;
    long long maxVal = 1'000'000'000;
    
    if (argc > 2) {
        minVal = atoll(argv[1]);
        maxVal = atoll(argv[2]);
    }
    
    // Get pattern type if specified (default: 0 - fully random)
    int pattern = argc > 3 ? atoi(argv[3]) : 0;
    
    long long A, B;
    
    switch (pattern) {
        case 1: // Equal numbers
            A = rnd.next(minVal, maxVal);
            B = A;
            break;
            
        case 2: // Powers of 10
            A = (long long)pow(10, rnd.next(0, 9));
            B = rnd.next(minVal, maxVal);
            break;
            
        case 3: // Numbers that sum to power of 10
            {
                long long sum = (long long)pow(10, rnd.next(1, 9));
                A = rnd.next(minVal, min(maxVal, sum-1));
                B = sum - A;
                if (B > maxVal) swap(A, B);
            }
            break;
            
        case 4: // Close to maximum
            A = maxVal - rnd.next(0, 100);
            B = maxVal - rnd.next(0, 100);
            break;
            
        case 5: // Close to minimum
            A = minVal + rnd.next(0, 100);
            B = minVal + rnd.next(0, 100);
            break;
            
        default: // Random numbers
            A = rnd.next(minVal, maxVal);
            B = rnd.next(minVal, maxVal);
    }
    
    cout << A << " " << B << endl;
    return 0;
}