"""Example usage of the Generator-Validator Agent System."""

import logging
from data_structures import Problem
from gv_system import GeneratorValidatorSystem

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def create_sample_problem() -> Problem:
    """Create a sample competitive programming problem."""
    return Problem(
        id="sample_001",
        title="Sum of Two Numbers",
        statement="""
        Given two integers A and B, output their sum.
        """,
        constraints="""
        1 ≤ A, B ≤ 10^9
        """,
        input_format="""
        The first line contains two integers A and B separated by a space.
        """,
        output_format="""
        Output a single integer representing A + B.
        """,
        sample_inputs=["5 3", "1000000000 999999999"],
        sample_outputs=["8", "1999999999"],
        time_limit=1.0,
        memory_limit=256
    )

def create_graph_problem() -> Problem:
    """Create a more complex graph problem."""
    return Problem(
        id="graph_001", 
        title="Connected Components",
        statement="""
        Given an undirected graph with N vertices and M edges, 
        find the number of connected components.
        """,
        constraints="""
        1 ≤ N ≤ 10^5
        0 ≤ M ≤ min(N*(N-1)/2, 2*10^5)
        1 ≤ u, v ≤ N for each edge (u, v)
        No self-loops or multiple edges
        """,
        input_format="""
        The first line contains two integers N and M.
        The next M lines each contain two integers u and v, 
        representing an edge between vertices u and v.
        """,
        output_format="""
        Output a single integer representing the number of connected components.
        """,
        sample_inputs=[
            "4 2\n1 2\n3 4",
            "5 3\n1 2\n2 3\n4 5"
        ],
        sample_outputs=["2", "2"],
        time_limit=2.0,
        memory_limit=256
    )

def main():
    """Main function demonstrating the G-V system."""
    print("Generator-Validator Agent System Demo")
    print("=" * 50)
    
    # Initialize the system
    gv_system = GeneratorValidatorSystem()
    
    try:
        # Example 1: Simple problem
        print("\n1. Testing with simple sum problem...")
        simple_problem = create_sample_problem()
        
        test_cases, gen_result, val_result = gv_system.generate_test_cases(
            simple_problem, max_iterations=2
        )
        
        print(f"Generated {len(test_cases)} test cases")
        print(f"Generator compilation: {'Success' if gen_result.compilation_successful else 'Failed'}")
        print(f"Validator compilation: {'Success' if val_result.compilation_successful else 'Failed'}")
        
        if gen_result.compilation_error:
            print(f"Generator error: {gen_result.compilation_error}")
        if val_result.compilation_error:
            print(f"Validator error: {val_result.compilation_error}")
        
        # Show some generated test cases
        print("\nSample generated test cases:")
        for i, tc in enumerate(test_cases[:3]):
            print(f"Test case {i+1}:")
            print(f"Input: {tc.input_data}")
            print(f"Description: {tc.description}")
            print()
        
        # Example 2: More complex graph problem
        print("\n2. Testing with graph problem...")
        graph_problem = create_graph_problem()
        
        test_cases2, gen_result2, val_result2 = gv_system.generate_test_cases(
            graph_problem, max_iterations=2
        )
        
        print(f"Generated {len(test_cases2)} test cases")
        print(f"Generator compilation: {'Success' if gen_result2.compilation_successful else 'Failed'}")
        print(f"Validator compilation: {'Success' if val_result2.compilation_successful else 'Failed'}")
        
        # Show generator and validator code snippets
        if gen_result2.generator_code:
            print("\nGenerator code snippet:")
            print(gen_result2.generator_code[:500] + "..." if len(gen_result2.generator_code) > 500 else gen_result2.generator_code)
        
        if val_result2.validator_code:
            print("\nValidator code snippet:")
            print(val_result2.validator_code[:500] + "..." if len(val_result2.validator_code) > 500 else val_result2.validator_code)
        
    except Exception as e:
        print(f"Error during execution: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Clean up
        gv_system.cleanup()
        print("\nDemo completed!")

if __name__ == "__main__":
    main()
