"""Data structures for the Generator-Validator Agent System."""

from dataclasses import dataclass
from typing import List, Optional, Dict, Any
from enum import Enum

class ValidationResult(Enum):
    """Validation result status."""
    VALID = "valid"
    INVALID = "invalid"
    ERROR = "error"

@dataclass
class Problem:
    """Represents a competitive programming problem."""
    id: str
    title: str
    statement: str
    constraints: str
    input_format: str
    output_format: str
    sample_inputs: List[str]
    sample_outputs: List[str]
    time_limit: float = 2.0  # seconds
    memory_limit: int = 256  # MB
    
    def get_full_description(self) -> str:
        """Get the complete problem description."""
        description = f"Problem: {self.title}\n\n"
        description += f"Statement:\n{self.statement}\n\n"
        description += f"Input Format:\n{self.input_format}\n\n"
        description += f"Output Format:\n{self.output_format}\n\n"
        description += f"Constraints:\n{self.constraints}\n\n"
        
        if self.sample_inputs and self.sample_outputs:
            description += "Sample Test Cases:\n"
            for i, (inp, out) in enumerate(zip(self.sample_inputs, self.sample_outputs)):
                description += f"Input {i+1}:\n{inp}\n"
                description += f"Output {i+1}:\n{out}\n\n"
        
        description += f"Time Limit: {self.time_limit} seconds\n"
        description += f"Memory Limit: {self.memory_limit} MB\n"
        
        return description

@dataclass
class TestCase:
    """Represents a test case."""
    input_data: str
    expected_output: str
    description: Optional[str] = None

@dataclass
class GeneratorCommand:
    """Represents a generator command with parameters."""
    command: str
    description: str
    expected_properties: List[str]

@dataclass
class GeneratorResult:
    """Result from the Generator Agent."""
    generator_code: str
    commands: List[GeneratorCommand]
    compilation_successful: bool = False
    compilation_error: Optional[str] = None
    test_cases: List[TestCase] = None

@dataclass
class ValidationError:
    """Represents a validation error."""
    error_type: str
    message: str
    line_number: Optional[int] = None
    constraint_violated: Optional[str] = None

@dataclass
class ValidatorResult:
    """Result from the Validator Agent."""
    validator_code: str
    compilation_successful: bool = False
    compilation_error: Optional[str] = None
    validation_results: List[ValidationResult] = None
    validation_errors: List[ValidationError] = None

@dataclass
class AgentFeedback:
    """Feedback provided to agents for improvement."""
    success: bool
    message: str
    errors: List[str] = None
    suggestions: List[str] = None
