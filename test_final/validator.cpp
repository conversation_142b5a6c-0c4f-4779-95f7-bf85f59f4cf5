#include "testlib.h"

int main(int argc, char* argv[]) {
    registerValidation(argc, argv);
    
    // Read first integer A
    int A = inf.readInt(1, 1000000000, "A");
    
    // Ensure there's exactly one space between numbers
    inf.readSpace();
    
    // Read second integer B
    int B = inf.readInt(1, 1000000000, "B");
    
    // Ensure line ends properly
    inf.readEoln();
    
    // Ensure no extra data after this line
    inf.readEof();
    
    ensuref(A + B <= 2000000000, 
            "Sum of A and B must fit in 32-bit signed integer");
    
    return 0;
}