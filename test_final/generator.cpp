#include "testlib.h"
#include <iostream>

using namespace std;

int main(int argc, char* argv[]) {
    registerGen(argc, argv, 1);
    
    // Parse command line arguments
    int minVal = 1;
    int maxVal = 1e9;
    
    if (argc > 1) {
        minVal = atoi(argv[1]);
        maxVal = atoi(argv[2]);
    }
    
    // Ensure constraints are met
    minVal = max(1, minVal);
    maxVal = min((int)1e9, maxVal);
    
    // Generate according to pattern if specified
    string pattern = argc > 3 ? argv[3] : "random";
    
    int A, B;
    
    if (pattern == "equal") {
        A = rnd.next(minVal, maxVal);
        B = A;
    }
    else if (pattern == "consecutive") {
        A = rnd.next(minVal, maxVal-1);
        B = A + 1;
    }
    else if (pattern == "max") {
        A = maxVal;
        B = maxVal;
    }
    else if (pattern == "min") {
        A = minVal;
        B = minVal;
    }
    else if (pattern == "power10") {
        int power = rnd.next(0, 9);
        A = (int)pow(10, power);
        B = rnd.next(minVal, maxVal);
    }
    else { // random
        A = rnd.next(minVal, maxVal);
        B = rnd.next(minVal, maxVal);
    }
    
    cout << A << " " << B << endl;
    
    return 0;
}