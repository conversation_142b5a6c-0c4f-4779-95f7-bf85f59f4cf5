"""Compilation and execution utilities for C++ code."""

import os
import subprocess
import tempfile
import logging
from typing import Tuple, Optional, List
from config import Config

logger = logging.getLogger(__name__)

class CompilerError(Exception):
    """Exception raised when compilation fails."""
    pass

class ExecutionError(Exception):
    """Exception raised when execution fails."""
    pass

class CppCompiler:
    """Utility class for compiling and executing C++ code."""
    
    def __init__(self):
        """Initialize the compiler."""
        self.temp_dir = tempfile.mkdtemp()
        logger.info(f"Created temporary directory: {self.temp_dir}")
    
    def compile_code(self, code: str, output_name: str = "program") -> str:
        """
        Compile C++ code and return the path to the executable.
        
        Args:
            code: C++ source code
            output_name: Name for the output executable
            
        Returns:
            Path to the compiled executable
            
        Raises:
            CompilerError: If compilation fails
        """
        # Write code to temporary file
        source_file = os.path.join(self.temp_dir, f"{output_name}.cpp")
        executable_file = os.path.join(self.temp_dir, output_name)
        
        try:
            with open(source_file, 'w') as f:
                f.write(code)
            
            # Compile the code
            compile_cmd = [
                "g++",
                "-std=c++17",
                "-O2",
                "-Wall",
                "-Wextra",
                source_file,
                "-o", executable_file
            ]
            
            result = subprocess.run(
                compile_cmd,
                capture_output=True,
                text=True,
                timeout=Config.COMPILE_TIMEOUT
            )
            
            if result.returncode != 0:
                error_msg = f"Compilation failed:\n{result.stderr}"
                logger.error(error_msg)
                raise CompilerError(error_msg)
            
            logger.info(f"Successfully compiled {output_name}")
            return executable_file
            
        except subprocess.TimeoutExpired:
            error_msg = f"Compilation timed out after {Config.COMPILE_TIMEOUT} seconds"
            logger.error(error_msg)
            raise CompilerError(error_msg)
        except Exception as e:
            error_msg = f"Compilation error: {str(e)}"
            logger.error(error_msg)
            raise CompilerError(error_msg)
    
    def execute_program(
        self, 
        executable_path: str, 
        input_data: str = "", 
        args: List[str] = None
    ) -> Tuple[str, str, int]:
        """
        Execute a compiled program.
        
        Args:
            executable_path: Path to the executable
            input_data: Input data to pass to stdin
            args: Command line arguments
            
        Returns:
            Tuple of (stdout, stderr, return_code)
            
        Raises:
            ExecutionError: If execution fails
        """
        if args is None:
            args = []
        
        try:
            cmd = [executable_path] + args
            
            result = subprocess.run(
                cmd,
                input=input_data,
                capture_output=True,
                text=True,
                timeout=Config.EXECUTION_TIMEOUT
            )
            
            return result.stdout, result.stderr, result.returncode
            
        except subprocess.TimeoutExpired:
            error_msg = f"Execution timed out after {Config.EXECUTION_TIMEOUT} seconds"
            logger.error(error_msg)
            raise ExecutionError(error_msg)
        except Exception as e:
            error_msg = f"Execution error: {str(e)}"
            logger.error(error_msg)
            raise ExecutionError(error_msg)
    
    def compile_and_run(
        self, 
        code: str, 
        input_data: str = "", 
        args: List[str] = None,
        program_name: str = "program"
    ) -> Tuple[str, str, int]:
        """
        Compile and run C++ code in one step.
        
        Args:
            code: C++ source code
            input_data: Input data for stdin
            args: Command line arguments
            program_name: Name for the program
            
        Returns:
            Tuple of (stdout, stderr, return_code)
        """
        executable_path = self.compile_code(code, program_name)
        return self.execute_program(executable_path, input_data, args)
    
    def cleanup(self):
        """Clean up temporary files."""
        try:
            import shutil
            shutil.rmtree(self.temp_dir)
            logger.info(f"Cleaned up temporary directory: {self.temp_dir}")
        except Exception as e:
            logger.warning(f"Failed to clean up temporary directory: {str(e)}")
    
    def __del__(self):
        """Cleanup on destruction."""
        self.cleanup()
