# Makefile for Generator-Validator Agent System

.PHONY: install test demo clean sample help

# Default target
help:
	@echo "Generator-Validator Agent System"
	@echo "================================"
	@echo ""
	@echo "Available targets:"
	@echo "  install    - Install Python dependencies"
	@echo "  test       - Run tests"
	@echo "  demo       - Run the demo"
	@echo "  sample     - Create sample problem JSON"
	@echo "  clean      - Clean up temporary files"
	@echo "  help       - Show this help message"
	@echo ""
	@echo "Usage examples:"
	@echo "  make install"
	@echo "  make sample"
	@echo "  make demo"
	@echo "  ./cli.py generate sample_problem.json"

# Install dependencies
install:
	@echo "Installing Python dependencies..."
	pip install -r requirements.txt
	@echo "Dependencies installed!"
	@echo ""
	@echo "Don't forget to set your OpenRouter API key in .env:"
	@echo "OPENROUTER_API_KEY=your_api_key_here"

# Run tests
test:
	@echo "Running basic tests..."
	python test_system.py basic
	@echo ""
	@echo "Running unit tests..."
	python -m unittest test_system -v

# Run demo
demo:
	@echo "Running demo..."
	python example_usage.py

# Create sample problem
sample:
	@echo "Creating sample problem JSON..."
	./cli.py sample
	@echo "Created sample_problem.json"
	@echo "You can now run: ./cli.py generate sample_problem.json"

# Clean up
clean:
	@echo "Cleaning up..."
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	rm -rf output/
	rm -rf temp_*
	@echo "Cleanup complete!"

# Check requirements
check:
	@echo "Checking system requirements..."
	@python --version || echo "❌ Python not found"
	@g++ --version > /dev/null 2>&1 && echo "✅ g++ compiler found" || echo "❌ g++ compiler not found"
	@pip show openai > /dev/null 2>&1 && echo "✅ OpenAI package installed" || echo "❌ OpenAI package not installed"
	@test -f .env && echo "✅ .env file found" || echo "❌ .env file not found"
