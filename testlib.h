/*
 * Simplified testlib.h for the Generator-Validator system
 * Based on the original testlib by <PERSON><PERSON><PERSON><PERSON><PERSON>
 */

#ifndef _TESTLIB_H_
#define _TESTLIB_H_

#include <iostream>
#include <fstream>
#include <sstream>
#include <string>
#include <vector>
#include <map>
#include <set>
#include <deque>
#include <queue>
#include <stack>
#include <algorithm>
#include <cmath>
#include <cstdlib>
#include <cstring>
#include <climits>
#include <cassert>
#include <ctime>
#include <random>

using namespace std;

const int _ok = 0;
const int _wa = 1;
const int _pe = 2;
const int _fail = 3;

class random_t {
private:
    mt19937 rnd;
    
public:
    random_t() : rnd(time(0)) {}
    random_t(int seed) : rnd(seed) {}
    
    int next(int n) {
        return uniform_int_distribution<int>(0, n-1)(rnd);
    }
    
    int next(int from, int to) {
        return uniform_int_distribution<int>(from, to)(rnd);
    }
    
    long long next(long long from, long long to) {
        return uniform_int_distribution<long long>(from, to)(rnd);
    }
    
    double next(double from, double to) {
        return uniform_real_distribution<double>(from, to)(rnd);
    }
};

random_t rnd;

void registerGen(int argc, char* argv[], int version) {
    if (argc > 1) {
        rnd = random_t(atoi(argv[1]));
    }
}

void quitf(int verdict, const char* format, ...) {
    va_list args;
    va_start(args, format);
    vprintf(format, args);
    va_end(args);
    printf("\n");
    exit(verdict);
}

class InStream {
private:
    istream* stream;
    string buffer;
    size_t pos;
    
public:
    InStream(istream& s) : stream(&s), pos(0) {}
    
    int readInt() {
        int x;
        *stream >> x;
        return x;
    }
    
    int readInt(int minv, int maxv) {
        int x = readInt();
        if (x < minv || x > maxv) {
            quitf(_wa, "Integer %d is out of range [%d, %d]", x, minv, maxv);
        }
        return x;
    }
    
    long long readLong() {
        long long x;
        *stream >> x;
        return x;
    }
    
    long long readLong(long long minv, long long maxv) {
        long long x = readLong();
        if (x < minv || x > maxv) {
            quitf(_wa, "Long %lld is out of range [%lld, %lld]", x, minv, maxv);
        }
        return x;
    }
    
    string readToken() {
        string s;
        *stream >> s;
        return s;
    }
    
    string readLine() {
        string s;
        getline(*stream, s);
        return s;
    }
    
    char readChar() {
        char c;
        *stream >> c;
        return c;
    }
    
    void readEoln() {
        string s;
        getline(*stream, s);
        if (!s.empty()) {
            quitf(_wa, "Expected end of line, but found: '%s'", s.c_str());
        }
    }
    
    void readEof() {
        string s;
        *stream >> s;
        if (!s.empty()) {
            quitf(_wa, "Expected end of file, but found: '%s'", s.c_str());
        }
    }
};

InStream inf(cin);
InStream ans(cin);
InStream ouf(cin);

void registerValidation(int argc, char* argv[]) {
    // Validation setup
}

void registerTestlibCmd(int argc, char* argv[]) {
    // Checker setup
}

#endif // _TESTLIB_H_
