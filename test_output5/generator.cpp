#include "testlib.h"
#include <iostream>

using namespace std;

int main(int argc, char* argv[]) {
    registerGen(argc, argv, 1);
    
    // Parse command line arguments
    int minVal = 1;
    int maxVal = 1e9;
    
    // If arguments provided, use them as range
    if (argc > 2) {
        minVal = atoi(argv[1]);
        maxVal = atoi(argv[2]);
    }
    
    // Special case for exact values
    if (argc > 4) {
        if (strcmp(argv[3], "exact") == 0) {
            int A = atoi(argv[4]);
            int B = atoi(argv[5]);
            println(A, B);
            return 0;
        }
    }
    
    // Special case for equal numbers
    if (argc > 3 && strcmp(argv[3], "equal") == 0) {
        int val = rnd.next(minVal, maxVal);
        println(val, val);
        return 0;
    }
    
    // Default random generation
    int A = rnd.next(minVal, maxVal);
    int B = rnd.next(minVal, maxVal);
    
    println(A, B);
    
    return 0;
}