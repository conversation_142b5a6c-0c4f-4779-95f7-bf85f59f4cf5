#include "testlib.h"

int main(int argc, char* argv[]) {
    registerValidation(argc, argv);
    
    // Read first integer A
    int A = inf.readInt(1, 1000000000, "A");
    
    // Must be followed by exactly one space
    inf.readSpace();
    
    // Read second integer B
    int B = inf.readInt(1, 1000000000, "B");
    
    // Must be followed by exactly one newline
    inf.readEoln();
    
    // No more input should remain
    inf.readEof();
    
    return 0;
}