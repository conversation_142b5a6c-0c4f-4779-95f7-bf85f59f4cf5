#include "testlib.h"
#include <iostream>

using namespace std;

int main(int argc, char* argv[]) {
    registerGen(argc, argv, 1);
    
    // Parse command line arguments
    // arg1: test type
    // arg2, arg3: optional range parameters
    string testType = argc > 1 ? string(argv[1]) : "random";
    
    long long A, B;
    
    if (testType == "random") {
        // Random numbers within the entire range
        A = rnd.next(1LL, (long long)1e9);
        B = rnd.next(1LL, (long long)1e9);
    }
    else if (testType == "small") {
        // Small numbers (1-100)
        A = rnd.next(1LL, 100LL);
        B = rnd.next(1LL, 100LL);
    }
    else if (testType == "large") {
        // Large numbers (10^8 to 10^9)
        A = rnd.next((long long)1e8, (long long)1e9);
        B = rnd.next((long long)1e8, (long long)1e9);
    }
    else if (testType == "equal") {
        // Equal numbers
        A = rnd.next(1LL, (long long)1e9);
        B = A;
    }
    else if (testType == "max") {
        // Maximum values
        A = (long long)1e9;
        B = (long long)1e9;
    }
    else if (testType == "min") {
        // Minimum values
        A = 1;
        B = 1;
    }
    else if (testType == "range") {
        // Numbers within specified range
        long long L = atoll(argv[2]);
        long long R = atoll(argv[3]);
        A = rnd.next(L, R);
        B = rnd.next(L, R);
    }
    else if (testType == "diff_magnitude") {
        // Numbers with different orders of magnitude
        int exp1 = rnd.next(0, 8);
        int exp2 = rnd.next(0, 8);
        while (exp1 == exp2) exp2 = rnd.next(0, 8);
        A = rnd.next((long long)pow(10, exp1), (long long)pow(10, exp1 + 1));
        B = rnd.next((long long)pow(10, exp2), (long long)pow(10, exp2 + 1));
    }
    
    cout << A << " " << B << endl;
    return 0;
}