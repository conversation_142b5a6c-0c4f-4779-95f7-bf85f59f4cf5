"""Generator Agent for creating test case generators."""

import logging
from typing import List, Optional
from llm_client import OpenRouterClient
from data_structures import Problem, GeneratorResult, GeneratorCommand, AgentFeedback
from config import Config

logger = logging.getLogger(__name__)

class GeneratorAgent:
    """Agent responsible for generating test case generators."""
    
    def __init__(self):
        """Initialize the Generator Agent."""
        self.client = OpenRouterClient()
        self.system_prompt = self._create_system_prompt()
    
    def _create_system_prompt(self) -> str:
        """Create the system prompt for the Generator Agent."""
        return """You are an expert competitive programming problem setter and test case generator. Your task is to create high-quality test case generators for competitive programming problems.

Your responsibilities:
1. Analyze the problem statement carefully to understand all constraints
2. Identify potential edge cases and corner cases
3. Design adversarial test cases that can catch common mistakes
4. Write a C++ generator program using testlib library
5. Provide generator commands that cover various data sizes and special cases

Guidelines:
- Use testlib library functions for random number generation and input parsing
- Ensure the generator respects all problem constraints
- Generate diverse test cases including small, medium, and large inputs
- Include edge cases like minimum/maximum values, empty inputs, etc.
- Make the generator deterministic based on command-line arguments
- Provide clear, descriptive commands with expected properties

Output format:
1. First, provide analysis of the problem constraints
2. Then provide the complete C++ generator code
3. Finally, provide approximately 20 generator commands with descriptions

Be thorough and precise in your implementation."""

    def generate_test_generator(
        self, 
        problem: Problem, 
        feedback: Optional[AgentFeedback] = None
    ) -> GeneratorResult:
        """
        Generate a test case generator for the given problem.
        
        Args:
            problem: The problem to generate tests for
            feedback: Optional feedback from previous attempts
            
        Returns:
            GeneratorResult containing the generator code and commands
        """
        logger.info(f"Generating test generator for problem: {problem.title}")
        
        # Create the user prompt
        user_prompt = self._create_user_prompt(problem, feedback)
        
        try:
            # Generate the response
            response = self.client.generate_with_system_prompt(
                system_prompt=self.system_prompt,
                user_prompt=user_prompt,
                model=Config.GENERATOR_MODEL,
                temperature=0.3,  # Lower temperature for more consistent code generation
                max_tokens=6000
            )
            
            # Parse the response
            return self._parse_generator_response(response)
            
        except Exception as e:
            logger.error(f"Failed to generate test generator: {str(e)}")
            return GeneratorResult(
                generator_code="",
                commands=[],
                compilation_successful=False,
                compilation_error=str(e)
            )
    
    def _create_user_prompt(self, problem: Problem, feedback: Optional[AgentFeedback]) -> str:
        """Create the user prompt for generator generation."""
        prompt = f"Generate a test case generator for the following competitive programming problem:\n\n"
        prompt += problem.get_full_description()
        
        if feedback and not feedback.success:
            prompt += f"\n\nPrevious attempt failed with the following issues:\n"
            prompt += f"Message: {feedback.message}\n"
            
            if feedback.errors:
                prompt += "Errors:\n"
                for error in feedback.errors:
                    prompt += f"- {error}\n"
            
            if feedback.suggestions:
                prompt += "Suggestions for improvement:\n"
                for suggestion in feedback.suggestions:
                    prompt += f"- {suggestion}\n"
            
            prompt += "\nPlease fix these issues in your new implementation.\n"
        
        prompt += "\nPlease provide:\n"
        prompt += "1. Analysis of constraints and potential edge cases\n"
        prompt += "2. Complete C++ generator code using testlib\n"
        prompt += "3. List of generator commands with descriptions\n"
        
        return prompt
    
    def _parse_generator_response(self, response: str) -> GeneratorResult:
        """Parse the LLM response to extract generator code and commands."""
        lines = response.split('\n')
        
        # Find code blocks
        generator_code = ""
        commands = []
        
        in_code_block = False
        code_lines = []
        
        for line in lines:
            if line.strip().startswith('```cpp') or line.strip().startswith('```c++'):
                in_code_block = True
                code_lines = []
            elif line.strip() == '```' and in_code_block:
                in_code_block = False
                if code_lines:
                    generator_code = '\n'.join(code_lines)
            elif in_code_block:
                code_lines.append(line)
        
        # Extract commands (look for command patterns)
        command_section = False
        for i, line in enumerate(lines):
            if 'command' in line.lower() and ('generator' in line.lower() or 'test' in line.lower()):
                command_section = True
                continue
            
            if command_section and line.strip():
                # Try to parse command lines
                if line.strip().startswith('./gen') or line.strip().startswith('gen'):
                    command_parts = line.strip().split(' - ', 1)
                    command = command_parts[0].strip()
                    description = command_parts[1].strip() if len(command_parts) > 1 else "Test case"
                    
                    commands.append(GeneratorCommand(
                        command=command,
                        description=description,
                        expected_properties=[]
                    ))
        
        # If no commands found, create some default ones
        if not commands:
            commands = self._create_default_commands()
        
        return GeneratorResult(
            generator_code=generator_code,
            commands=commands,
            compilation_successful=bool(generator_code),
            compilation_error=None if generator_code else "No generator code found in response"
        )
    
    def _create_default_commands(self) -> List[GeneratorCommand]:
        """Create default generator commands if none were parsed."""
        return [
            GeneratorCommand("./gen 1 10", "Small test case", ["n=10"]),
            GeneratorCommand("./gen 2 100", "Medium test case", ["n=100"]),
            GeneratorCommand("./gen 3 1000", "Large test case", ["n=1000"]),
            GeneratorCommand("./gen 4 1", "Minimum case", ["n=1"]),
            GeneratorCommand("./gen 5 100000", "Maximum case", ["n=100000"]),
        ]
