#include "testlib.h"
#include <iostream>

using namespace std;

int main(int argc, char* argv[]) {
    registerGen(argc, argv, 1);
    
    // Parse command line arguments
    // arg1: minimum possible value
    // arg2: maximum possible value
    // arg3: type of test case (if specified)
    
    long long minVal = 1;
    long long maxVal = 1000000000;
    
    if (argc > 2) {
        minVal = atoll(argv[1]);
        maxVal = atoll(argv[2]);
    }
    
    string testType = argc > 3 ? argv[3] : "random";
    
    long long A, B;
    
    if (testType == "equal") {
        // Generate equal numbers
        A = B = rnd.next(minVal, maxVal);
    }
    else if (testType == "max") {
        // Generate numbers close to maximum
        A = rnd.next(maxVal - 1000, maxVal);
        B = rnd.next(maxVal - 1000, maxVal);
    }
    else if (testType == "min") {
        // Generate numbers close to minimum
        A = rnd.next(minVal, minVal + 1000);
        B = rnd.next(minVal, minVal + 1000);
    }
    else if (testType == "opposite") {
        // One small, one large
        A = rnd.next(minVal, minVal + 1000);
        B = rnd.next(maxVal - 1000, maxVal);
    }
    else {
        // Random numbers in the given range
        A = rnd.next(minVal, maxVal);
        B = rnd.next(minVal, maxVal);
    }
    
    cout << A << " " << B << endl;
    
    return 0;
}