#include "testlib.h"

int main(int argc, char* argv[]) {
    registerValidation(argc, argv);
    
    // Read first integer A
    int A = inf.readInt(1, 1000000000, "A");
    
    // Must be followed by exactly one space
    inf.readSpace();
    
    // Read second integer B
    int B = inf.readInt(1, 1000000000, "B");
    
    // Must be followed by end of line
    inf.readEoln();
    
    // Must be end of file (no extra data)
    inf.readEof();
    
    return 0;
}