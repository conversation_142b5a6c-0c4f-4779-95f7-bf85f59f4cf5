./gen random 1 100 1 # Test case
./gen random 1 100 2 # Test case
./gen random 1 1000 3 # Test case
./gen random 1000 1000000 4 # Test case
./gen random 1000 1000000 5 # Test case
./gen random 10000 100000 6 # Test case
./gen random 100000000 1000000000 7 # Test case
./gen random 500000000 1000000000 8 # Test case
./gen random 900000000 1000000000 9 # Test case
./gen equal 1 100 10 # Test case
./gen equal 1000 1000000 11 # Test case
./gen equal 100000000 1000000000 12 # Test case
./gen max 1 1000000000 13 # Test case
./gen min 1 1000000000 14 # Test case
./gen maxmin 1 1000000000 15 # Test case
./gen close 1 100 16 # Test case
./gen close 1000 1000000 17 # Test case
./gen close 100000000 1000000000 18 # Test case
./gen power10 1 1000000000 19 # Test case
./gen power10 1 1000000000 20 # Test case
