#include "testlib.h"
#include <iostream>

using namespace std;

int main(int argc, char* argv[]) {
    registerGen(argc, argv, 1);
    
    // Parse command line arguments
    // arg1: test type
    // arg2: minimum value
    // arg3: maximum value
    string testType = argc > 1 ? string(argv[1]) : "random";
    int minVal = argc > 2 ? atoi(argv[2]) : 1;
    int maxVal = argc > 3 ? atoi(argv[3]) : 1000000000;
    
    int A, B;
    
    if (testType == "random") {
        // Generate random numbers within the given range
        A = rnd.next(minVal, maxVal);
        B = rnd.next(minVal, maxVal);
    }
    else if (testType == "equal") {
        // Generate equal numbers
        A = rnd.next(minVal, maxVal);
        B = A;
    }
    else if (testType == "max") {
        // Generate maximum values
        A = maxVal;
        B = maxVal;
    }
    else if (testType == "min") {
        // Generate minimum values
        A = minVal;
        B = minVal;
    }
    else if (testType == "maxmin") {
        // One maximum, one minimum
        A = maxVal;
        B = minVal;
    }
    else if (testType == "close") {
        // Generate numbers close to each other
        A = rnd.next(minVal, maxVal);
        B = A + rnd.next(-10, 10);
        B = max(minVal, min(B, maxVal));
    }
    else if (testType == "power10") {
        // Numbers summing to power of 10
        int power = rnd.next(1, 9);
        int sum = pow(10, power);
        A = rnd.next(1, sum - 1);
        B = sum - A;
    }
    
    cout << A << " " << B << endl;
    return 0;
}