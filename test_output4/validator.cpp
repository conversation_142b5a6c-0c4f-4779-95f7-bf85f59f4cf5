#include "testlib.h"

int main(int argc, char* argv[]) {
    registerValidation(argc, argv);
    
    // Read first integer A
    int A = inf.readInt(1, 1000000000, "A");
    
    // Ensure exactly one space between numbers
    inf.readSpace();
    
    // Read second integer B
    int B = inf.readInt(1, 1000000000, "B");
    
    // Ensure line ends here
    inf.readEoln();
    
    // Ensure no extra lines at end of file
    inf.readEof();
    
    return 0;
}