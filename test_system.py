#!/usr/bin/env python3
"""Test script for the Generator-Validator Agent System."""

import unittest
import tempfile
import os
from unittest.mock import Mock, patch

from data_structures import Problem, TestCase
from generator_agent import GeneratorAgent
from validator_agent import ValidatorAgent
from compiler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CompilerError
from gv_system import GeneratorValidatorSystem

class TestDataStructures(unittest.TestCase):
    """Test data structures."""
    
    def test_problem_creation(self):
        """Test Problem creation and methods."""
        problem = Problem(
            id="test_001",
            title="Test Problem",
            statement="Test statement",
            constraints="Test constraints",
            input_format="Test input format",
            output_format="Test output format",
            sample_inputs=["1 2"],
            sample_outputs=["3"]
        )
        
        self.assertEqual(problem.id, "test_001")
        self.assertEqual(problem.title, "Test Problem")
        self.assertIn("Test Problem", problem.get_full_description())
        self.assertIn("Test statement", problem.get_full_description())

class TestCompiler(unittest.TestCase):
    """Test C++ compiler functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.compiler = CppCompiler()
    
    def tearDown(self):
        """Clean up after tests."""
        self.compiler.cleanup()
    
    def test_simple_compilation(self):
        """Test compiling simple C++ code."""
        simple_code = """
        #include <iostream>
        using namespace std;
        
        int main() {
            cout << "Hello World" << endl;
            return 0;
        }
        """
        
        try:
            executable = self.compiler.compile_code(simple_code, "hello")
            self.assertTrue(os.path.exists(executable))
            
            # Test execution
            stdout, stderr, returncode = self.compiler.execute_program(executable)
            self.assertEqual(returncode, 0)
            self.assertIn("Hello World", stdout)
            
        except CompilerError as e:
            self.skipTest(f"C++ compiler not available: {e}")
    
    def test_compilation_error(self):
        """Test handling of compilation errors."""
        invalid_code = """
        #include <iostream>
        using namespace std;
        
        int main() {
            cout << "Missing semicolon"
            return 0;
        }
        """
        
        with self.assertRaises(CompilerError):
            self.compiler.compile_code(invalid_code, "invalid")

class TestAgents(unittest.TestCase):
    """Test agent functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.sample_problem = Problem(
            id="test_001",
            title="Sum Problem",
            statement="Add two numbers",
            constraints="1 ≤ A, B ≤ 100",
            input_format="Two integers A B",
            output_format="Single integer A+B",
            sample_inputs=["1 2"],
            sample_outputs=["3"]
        )
    
    @patch('llm_client.OpenRouterClient')
    def test_generator_agent_mock(self, mock_client_class):
        """Test generator agent with mocked LLM client."""
        # Mock the LLM response
        mock_client = Mock()
        mock_client.generate_with_system_prompt.return_value = """
        Analysis: This is a simple addition problem.
        
        ```cpp
        #include <iostream>
        using namespace std;
        
        int main() {
            int a = 1, b = 2;
            cout << a << " " << b << endl;
            return 0;
        }
        ```
        
        Commands:
        ./gen 1 - Small test case
        ./gen 2 - Medium test case
        """
        
        mock_client_class.return_value = mock_client
        
        generator = GeneratorAgent()
        result = generator.generate_test_generator(self.sample_problem)
        
        self.assertIsNotNone(result.generator_code)
        self.assertGreater(len(result.commands), 0)
        self.assertTrue(result.compilation_successful)
    
    @patch('llm_client.OpenRouterClient')
    def test_validator_agent_mock(self, mock_client_class):
        """Test validator agent with mocked LLM client."""
        # Mock the LLM response
        mock_client = Mock()
        mock_client.generate_with_system_prompt.return_value = """
        Analysis: Need to validate integer ranges.
        
        ```cpp
        #include <iostream>
        using namespace std;
        
        int main() {
            int a, b;
            cin >> a >> b;
            if (a < 1 || a > 100 || b < 1 || b > 100) {
                return 1;
            }
            return 0;
        }
        ```
        """
        
        mock_client_class.return_value = mock_client
        
        validator = ValidatorAgent()
        result = validator.generate_validator(self.sample_problem)
        
        self.assertIsNotNone(result.validator_code)
        self.assertTrue(result.compilation_successful)

class TestIntegration(unittest.TestCase):
    """Integration tests."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.sample_problem = Problem(
            id="integration_001",
            title="Simple Addition",
            statement="Given two integers, output their sum",
            constraints="1 ≤ A, B ≤ 10",
            input_format="Two integers A and B",
            output_format="Single integer A+B",
            sample_inputs=["1 2", "5 5"],
            sample_outputs=["3", "10"]
        )
    
    @patch('llm_client.OpenRouterClient')
    def test_system_integration_mock(self, mock_client_class):
        """Test full system integration with mocked LLM."""
        # Mock responses for both generator and validator
        mock_client = Mock()
        
        def mock_generate(system_prompt, user_prompt, **kwargs):
            if "generator" in system_prompt.lower():
                return """
                Analysis: Simple addition problem with small constraints.
                
                ```cpp
                #include <iostream>
                #include <cstdlib>
                using namespace std;
                
                int main(int argc, char* argv[]) {
                    int seed = argc > 1 ? atoi(argv[1]) : 1;
                    srand(seed);
                    
                    int a = 1 + rand() % 10;
                    int b = 1 + rand() % 10;
                    cout << a << " " << b << endl;
                    return 0;
                }
                ```
                
                Commands:
                ./gen 1 - Random small case
                ./gen 2 - Another random case
                """
            else:  # validator
                return """
                Analysis: Validate integer ranges 1-10.
                
                ```cpp
                #include <iostream>
                using namespace std;
                
                int main() {
                    int a, b;
                    if (!(cin >> a >> b)) return 1;
                    if (a < 1 || a > 10 || b < 1 || b > 10) return 1;
                    return 0;
                }
                ```
                """
        
        mock_client.generate_with_system_prompt.side_effect = mock_generate
        mock_client_class.return_value = mock_client
        
        # Test the system
        try:
            gv_system = GeneratorValidatorSystem()
            test_cases, gen_result, val_result = gv_system.generate_test_cases(
                self.sample_problem, max_iterations=1
            )
            
            # Basic checks
            self.assertIsNotNone(gen_result)
            self.assertIsNotNone(val_result)
            self.assertIsInstance(test_cases, list)
            
            gv_system.cleanup()
            
        except Exception as e:
            # If compilation fails (no g++), skip the test
            if "g++" in str(e) or "compiler" in str(e).lower():
                self.skipTest(f"C++ compiler not available: {e}")
            else:
                raise

def run_basic_test():
    """Run a basic test without unittest framework."""
    print("Running basic system test...")
    
    # Test data structures
    problem = Problem(
        id="basic_001",
        title="Basic Test",
        statement="Test problem",
        constraints="Test constraints",
        input_format="Test input",
        output_format="Test output",
        sample_inputs=["test"],
        sample_outputs=["test"]
    )
    
    print(f"✓ Created problem: {problem.title}")
    
    # Test compiler (if available)
    try:
        compiler = CppCompiler()
        simple_code = '#include <iostream>\nint main() { std::cout << "test"; return 0; }'
        executable = compiler.compile_code(simple_code, "test")
        stdout, stderr, returncode = compiler.execute_program(executable)
        
        if returncode == 0 and "test" in stdout:
            print("✓ C++ compiler working")
        else:
            print("✗ C++ execution failed")
        
        compiler.cleanup()
        
    except Exception as e:
        print(f"⚠ C++ compiler test failed: {e}")
    
    print("Basic test completed!")

if __name__ == '__main__':
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == 'basic':
        run_basic_test()
    else:
        unittest.main()
