#include "testlib.h"
#include <vector>
#include <set>
using namespace std;

// Generate a random graph with given parameters
void generateGraph(int n, int m, bool connected = false) {
    printf("%d %d\n", n, m);
    
    set<pair<int, int>> edges;
    
    // If connected is required, first create a spanning tree
    if (connected && n > 1) {
        vector<int> perm(n);
        for(int i = 0; i < n; i++) perm[i] = i + 1;
        shuffle(perm.begin(), perm.end());
        
        // Add n-1 edges to ensure connectivity
        for(int i = 1; i < n; i++) {
            int u = perm[i-1];
            int v = perm[i];
            if (u > v) swap(u, v);
            edges.insert({u, v});
        }
    }
    
    // Add remaining random edges
    while(edges.size() < m) {
        int u = rnd.next(1, n);
        int v = rnd.next(1, n);
        if (u == v) continue;  // avoid self-loops
        if (u > v) swap(u, v);
        edges.insert({u, v});  // set automatically handles duplicates
    }
    
    // Output edges
    for(const auto& edge : edges) {
        printf("%d %d\n", edge.first, edge.second);
    }
}

// Generate a graph with specific component sizes
void generateComponents(const vector<int>& sizes) {
    int n = 0;
    for(int size : sizes) n += size;
    
    int m = 0;
    vector<pair<int,int>> edges;
    int vertex = 1;
    
    // Generate complete components of given sizes
    for(int size : sizes) {
        for(int i = 0; i < size; i++) {
            for(int j = i+1; j < size; j++) {
                edges.push_back({vertex + i, vertex + j});
                m++;
            }
        }
        vertex += size;
    }
    
    printf("%d %d\n", n, m);
    for(const auto& edge : edges) {
        printf("%d %d\n", edge.first, edge.second);
    }
}

int main(int argc, char* argv[]) {
    registerGen(argc, argv, 1);
    
    string mode = argv[1];
    
    if (mode == "random") {
        // Random graph: random N M
        int n = atoi(argv[2]);
        int m = atoi(argv[3]);
        generateGraph(n, m);
    }
    else if (mode == "connected") {
        // Connected random graph
        int n = atoi(argv[2]);
        int m = atoi(argv[3]);
        generateGraph(n, m, true);
    }
    else if (mode == "components") {
        // Specific component sizes
        vector<int> sizes;
        for(int i = 2; argv[i]; i++) {
            sizes.push_back(atoi(argv[i]));
        }
        generateComponents(sizes);
    }
    
    return 0;
}