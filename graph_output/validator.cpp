#include "testlib.h"
#include <set>
#include <utility>

using namespace std;

// Helper function to create a unique edge identifier
pair<int, int> makeEdge(int u, int v) {
    return make_pair(min(u, v), max(u, v));
}

int main(int argc, char* argv[]) {
    registerValidation(argc, argv);
    
    // Read N and M from first line
    int n = inf.readInt(1, 1000, "N");
    inf.readSpace();
    
    // Calculate maximum possible edges for n vertices
    long long maxPossibleEdges = (long long)n * (n - 1) / 2;
    int maxEdges = min((int)maxPossibleEdges, 5000);
    
    int m = inf.readInt(0, maxEdges, "M");
    inf.readEoln();
    
    // Set to store edges for checking duplicates
    set<pair<int, int>> edges;
    
    // Read and validate M edges
    for (int i = 0; i < m; i++) {
        int u = inf.readInt(1, n, "u");
        inf.readSpace();
        int v = inf.readInt(1, n, "v");
        inf.readEoln();
        
        // Check for self-loops
        if (u == v) {
            quitf(_wa, "Self-loop detected on line %d: vertex %d", i + 2, u);
        }
        
        // Check for multiple edges
        pair<int, int> edge = makeEdge(u, v);
        if (edges.count(edge) > 0) {
            quitf(_wa, "Multiple edge detected on line %d: edge %d-%d already exists", 
                  i + 2, u, v);
        }
        
        edges.insert(edge);
    }
    
    // Ensure we've reached the end of input
    inf.readEof();
    
    return 0;
}