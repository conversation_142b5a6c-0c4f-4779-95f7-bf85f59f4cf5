#include "testlib.h"
#include <iostream>

using namespace std;

int main(int argc, char* argv[]) {
    registerGen(argc, argv, 1);
    
    // Parse command line arguments
    int minVal = 1;
    int maxVal = 1'000'000'000;
    
    if (argc > 1) {
        minVal = atoi(argv[1]);
        maxVal = atoi(argv[2]);
    }
    
    // Ensure constraints are respected
    minVal = max(1, minVal);
    maxVal = min(1'000'000'000, maxVal);
    
    // Generate numbers based on pattern parameter if provided
    string pattern = argc > 3 ? argv[3] : "random";
    
    int A, B;
    
    if (pattern == "equal") {
        A = rnd.next(minVal, maxVal);
        B = A;
    }
    else if (pattern == "max") {
        A = maxVal;
        B = maxVal;
    }
    else if (pattern == "min") {
        A = minVal;
        B = minVal;
    }
    else if (pattern == "maxmin") {
        A = maxVal;
        B = minVal;
    }
    else if (pattern == "power10") {
        int power = rnd.next(0, 9);
        A = pow(10, power);
        B = rnd.next(minVal, maxVal);
    }
    else {
        // Default random case
        A = rnd.next(minVal, maxVal);
        B = rnd.next(minVal, maxVal);
    }
    
    cout << A << " " << B << endl;
    
    return 0;
}