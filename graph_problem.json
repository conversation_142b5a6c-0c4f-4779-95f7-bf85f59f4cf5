{"id": "graph_001", "title": "Connected Components", "statement": "Given an undirected graph with N vertices and M edges, find the number of connected components.", "constraints": "1 ≤ N ≤ 1000\n0 ≤ M ≤ min(N*(N-1)/2, 5000)\n1 ≤ u, v ≤ N for each edge (u, v)\nNo self-loops or multiple edges", "input_format": "The first line contains two integers N and M.\nThe next M lines each contain two integers u and v, representing an edge between vertices u and v.", "output_format": "Output a single integer representing the number of connected components.", "sample_inputs": ["4 2\n1 2\n3 4", "5 3\n1 2\n2 3\n4 5"], "sample_outputs": ["2", "2"], "time_limit": 2.0, "memory_limit": 256}