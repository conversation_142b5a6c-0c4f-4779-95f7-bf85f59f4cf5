"""OpenRouter LLM client for the Generator-Validator Agent System."""

import openai
from typing import List, Dict, Any, Optional
from config import Config
import time
import logging

logger = logging.getLogger(__name__)

class OpenRouterClient:
    """Client for interacting with OpenRouter API."""
    
    def __init__(self):
        """Initialize the OpenRouter client."""
        self.client = openai.OpenAI(
            base_url=Config.OPENROUTER_BASE_URL,
            api_key=Config.OPENROUTER_API_KEY,
        )
    
    def generate_completion(
        self,
        messages: List[Dict[str, str]],
        model: str = Config.DEFAULT_MODEL,
        temperature: float = 0.7,
        max_tokens: int = 4000,
        retry_count: int = 0
    ) -> str:
        """
        Generate a completion using the OpenRouter API.
        
        Args:
            messages: List of message dictionaries with 'role' and 'content'
            model: Model to use for generation
            temperature: Sampling temperature
            max_tokens: Maximum tokens to generate
            retry_count: Current retry attempt
            
        Returns:
            Generated text response
            
        Raises:
            Exception: If API call fails after max retries
        """
        try:
            response = self.client.chat.completions.create(
                model=model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens,
                timeout=Config.TIMEOUT
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            logger.error(f"API call failed (attempt {retry_count + 1}): {str(e)}")
            
            if retry_count < Config.MAX_RETRIES:
                # Exponential backoff
                wait_time = 2 ** retry_count
                logger.info(f"Retrying in {wait_time} seconds...")
                time.sleep(wait_time)
                
                return self.generate_completion(
                    messages=messages,
                    model=model,
                    temperature=temperature,
                    max_tokens=max_tokens,
                    retry_count=retry_count + 1
                )
            else:
                raise Exception(f"Failed to generate completion after {Config.MAX_RETRIES} retries: {str(e)}")
    
    def generate_with_system_prompt(
        self,
        system_prompt: str,
        user_prompt: str,
        model: str = Config.DEFAULT_MODEL,
        temperature: float = 0.7,
        max_tokens: int = 4000
    ) -> str:
        """
        Generate completion with system and user prompts.
        
        Args:
            system_prompt: System prompt to set context
            user_prompt: User prompt with the actual request
            model: Model to use
            temperature: Sampling temperature
            max_tokens: Maximum tokens to generate
            
        Returns:
            Generated response
        """
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]
        
        return self.generate_completion(
            messages=messages,
            model=model,
            temperature=temperature,
            max_tokens=max_tokens
        )
