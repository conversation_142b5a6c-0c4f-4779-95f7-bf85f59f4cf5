#!/usr/bin/env python3
"""Command-line interface for the Generator-Validator Agent System."""

import argparse
import json
import logging
import sys
from pathlib import Path
from typing import Dict, Any

from data_structures import Problem
from gv_system import GeneratorValidatorSystem

def setup_logging(verbose: bool = False):
    """Set up logging configuration."""
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def load_problem_from_json(file_path: str) -> Problem:
    """Load a problem from a JSON file."""
    with open(file_path, 'r') as f:
        data = json.load(f)
    
    return Problem(
        id=data.get('id', 'unknown'),
        title=data.get('title', 'Untitled'),
        statement=data.get('statement', ''),
        constraints=data.get('constraints', ''),
        input_format=data.get('input_format', ''),
        output_format=data.get('output_format', ''),
        sample_inputs=data.get('sample_inputs', []),
        sample_outputs=data.get('sample_outputs', []),
        time_limit=data.get('time_limit', 2.0),
        memory_limit=data.get('memory_limit', 256)
    )

def save_results(output_dir: str, problem: Problem, test_cases, gen_result, val_result):
    """Save the generated results to files."""
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    # Save generator code
    if gen_result.generator_code:
        with open(output_path / 'generator.cpp', 'w') as f:
            f.write(gen_result.generator_code)
    
    # Save validator code
    if val_result.validator_code:
        with open(output_path / 'validator.cpp', 'w') as f:
            f.write(val_result.validator_code)
    
    # Save generator commands
    if gen_result.commands:
        with open(output_path / 'commands.txt', 'w') as f:
            for cmd in gen_result.commands:
                f.write(f"{cmd.command} # {cmd.description}\n")
    
    # Save test cases
    if test_cases:
        test_cases_dir = output_path / 'test_cases'
        test_cases_dir.mkdir(exist_ok=True)
        
        for i, tc in enumerate(test_cases):
            with open(test_cases_dir / f'input_{i+1:02d}.txt', 'w') as f:
                f.write(tc.input_data)
            
            if tc.expected_output:
                with open(test_cases_dir / f'output_{i+1:02d}.txt', 'w') as f:
                    f.write(tc.expected_output)
    
    # Save summary
    summary = {
        'problem_id': problem.id,
        'problem_title': problem.title,
        'num_test_cases': len(test_cases),
        'generator_compilation': gen_result.compilation_successful,
        'validator_compilation': val_result.compilation_successful,
        'generator_error': gen_result.compilation_error,
        'validator_error': val_result.compilation_error
    }
    
    with open(output_path / 'summary.json', 'w') as f:
        json.dump(summary, f, indent=2)
    
    print(f"Results saved to: {output_path}")

def create_sample_problem_json():
    """Create a sample problem JSON file."""
    sample_problem = {
        "id": "sample_001",
        "title": "Sum of Two Numbers",
        "statement": "Given two integers A and B, output their sum.",
        "constraints": "1 ≤ A, B ≤ 10^9",
        "input_format": "The first line contains two integers A and B separated by a space.",
        "output_format": "Output a single integer representing A + B.",
        "sample_inputs": ["5 3", "1000000000 999999999"],
        "sample_outputs": ["8", "1999999999"],
        "time_limit": 1.0,
        "memory_limit": 256
    }
    
    with open('sample_problem.json', 'w') as f:
        json.dump(sample_problem, f, indent=2)
    
    print("Created sample_problem.json")

def main():
    """Main CLI function."""
    parser = argparse.ArgumentParser(
        description="Generator-Validator Agent System for Competitive Programming"
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Generate command
    gen_parser = subparsers.add_parser('generate', help='Generate test cases for a problem')
    gen_parser.add_argument('problem_file', help='JSON file containing the problem definition')
    gen_parser.add_argument('-o', '--output', default='output', help='Output directory')
    gen_parser.add_argument('-i', '--iterations', type=int, default=3, help='Maximum iterations')
    gen_parser.add_argument('-v', '--verbose', action='store_true', help='Verbose logging')
    
    # Sample command
    sample_parser = subparsers.add_parser('sample', help='Create a sample problem JSON file')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    if args.command == 'sample':
        create_sample_problem_json()
        return
    
    if args.command == 'generate':
        setup_logging(args.verbose)
        
        try:
            # Load problem
            print(f"Loading problem from: {args.problem_file}")
            problem = load_problem_from_json(args.problem_file)
            print(f"Loaded problem: {problem.title}")
            
            # Initialize system
            print("Initializing Generator-Validator system...")
            gv_system = GeneratorValidatorSystem()
            
            # Generate test cases
            print(f"Generating test cases (max {args.iterations} iterations)...")
            test_cases, gen_result, val_result = gv_system.generate_test_cases(
                problem, max_iterations=args.iterations
            )
            
            # Print results
            print(f"\nResults:")
            print(f"- Generated {len(test_cases)} test cases")
            print(f"- Generator compilation: {'✓' if gen_result.compilation_successful else '✗'}")
            print(f"- Validator compilation: {'✓' if val_result.compilation_successful else '✗'}")
            
            if gen_result.compilation_error:
                print(f"- Generator error: {gen_result.compilation_error}")
            
            if val_result.compilation_error:
                print(f"- Validator error: {val_result.compilation_error}")
            
            # Save results
            save_results(args.output, problem, test_cases, gen_result, val_result)
            
            # Cleanup
            gv_system.cleanup()
            
        except FileNotFoundError:
            print(f"Error: Problem file '{args.problem_file}' not found")
            sys.exit(1)
        except json.JSONDecodeError as e:
            print(f"Error: Invalid JSON in problem file: {e}")
            sys.exit(1)
        except Exception as e:
            print(f"Error: {e}")
            import traceback
            traceback.print_exc()
            sys.exit(1)

if __name__ == '__main__':
    main()
