"""Validator Agent for validating test cases."""

import logging
from typing import List, Optional
from llm_client import OpenRouterClient
from data_structures import Problem, <PERSON>idator<PERSON><PERSON>ult, <PERSON><PERSON>tionError, AgentF<PERSON>back, ValidationResult
from config import Config

logger = logging.getLogger(__name__)

class ValidatorAgent:
    """Agent responsible for validating test cases."""
    
    def __init__(self):
        """Initialize the Validator Agent."""
        self.client = OpenRouterClient()
        self.system_prompt = self._create_system_prompt()
    
    def _create_system_prompt(self) -> str:
        """Create the system prompt for the Validator Agent."""
        return """You are an expert competitive programming judge and test case validator. Your task is to create validators that check if test cases satisfy all problem constraints.

Your responsibilities:
1. Carefully analyze the problem statement to identify ALL constraints
2. Write a C++ validator program that checks every constraint
3. Provide detailed error messages when constraints are violated
4. Handle edge cases and boundary conditions properly
5. Ensure the validator is robust and catches all invalid inputs

Guidelines:
- Use testlib library for input reading and validation
- Check data ranges, format requirements, and structural constraints
- Provide specific error messages with line numbers when possible
- Handle all edge cases mentioned in the problem
- Be thorough - missing a constraint can lead to invalid test cases
- Use appropriate testlib functions like readInt(), readString(), etc.
- Call quitf() with appropriate verdict when validation fails

Validation verdicts:
- Use _ok for valid input
- Use _wa for constraint violations
- Use _pe for format errors

Output format:
1. First, provide analysis of all constraints that need to be checked
2. Then provide the complete C++ validator code
3. Explain the validation logic briefly

Be extremely thorough in identifying and checking constraints."""

    def generate_validator(
        self, 
        problem: Problem, 
        feedback: Optional[AgentFeedback] = None
    ) -> ValidatorResult:
        """
        Generate a validator for the given problem.
        
        Args:
            problem: The problem to create validator for
            feedback: Optional feedback from previous attempts
            
        Returns:
            ValidatorResult containing the validator code
        """
        logger.info(f"Generating validator for problem: {problem.title}")
        
        # Create the user prompt
        user_prompt = self._create_user_prompt(problem, feedback)
        
        try:
            # Generate the response
            response = self.client.generate_with_system_prompt(
                system_prompt=self.system_prompt,
                user_prompt=user_prompt,
                model=Config.VALIDATOR_MODEL,
                temperature=0.2,  # Very low temperature for precise validation logic
                max_tokens=4000
            )
            
            # Parse the response
            return self._parse_validator_response(response)
            
        except Exception as e:
            logger.error(f"Failed to generate validator: {str(e)}")
            return ValidatorResult(
                validator_code="",
                compilation_successful=False,
                compilation_error=str(e)
            )
    
    def _create_user_prompt(self, problem: Problem, feedback: Optional[AgentFeedback]) -> str:
        """Create the user prompt for validator generation."""
        prompt = f"Create a validator for the following competitive programming problem:\n\n"
        prompt += problem.get_full_description()
        
        if feedback and not feedback.success:
            prompt += f"\n\nPrevious validator failed with the following issues:\n"
            prompt += f"Message: {feedback.message}\n"
            
            if feedback.errors:
                prompt += "Errors:\n"
                for error in feedback.errors:
                    prompt += f"- {error}\n"
            
            if feedback.suggestions:
                prompt += "Suggestions for improvement:\n"
                for suggestion in feedback.suggestions:
                    prompt += f"- {suggestion}\n"
            
            prompt += "\nPlease fix these issues in your new validator.\n"
        
        prompt += "\nPlease provide:\n"
        prompt += "1. Complete analysis of all constraints to validate\n"
        prompt += "2. Complete C++ validator code using testlib\n"
        prompt += "3. Brief explanation of the validation logic\n"
        
        return prompt
    
    def _parse_validator_response(self, response: str) -> ValidatorResult:
        """Parse the LLM response to extract validator code."""
        lines = response.split('\n')
        
        # Find code blocks
        validator_code = ""
        
        in_code_block = False
        code_lines = []
        
        for line in lines:
            if line.strip().startswith('```cpp') or line.strip().startswith('```c++'):
                in_code_block = True
                code_lines = []
            elif line.strip() == '```' and in_code_block:
                in_code_block = False
                if code_lines:
                    validator_code = '\n'.join(code_lines)
                    break  # Take the first code block
            elif in_code_block:
                code_lines.append(line)
        
        return ValidatorResult(
            validator_code=validator_code,
            compilation_successful=bool(validator_code),
            compilation_error=None if validator_code else "No validator code found in response"
        )
    
    def validate_test_case(self, validator_code: str, test_input: str) -> ValidationResult:
        """
        Validate a test case using the validator code.
        
        Args:
            validator_code: The compiled validator code
            test_input: The test input to validate
            
        Returns:
            ValidationResult indicating if the test case is valid
        """
        # This would typically involve compiling and running the validator
        # For now, we'll return a placeholder implementation
        logger.info("Validating test case...")
        
        # In a real implementation, this would:
        # 1. Compile the validator code
        # 2. Run it with the test input
        # 3. Parse the output to determine validity
        
        return ValidationResult.VALID
